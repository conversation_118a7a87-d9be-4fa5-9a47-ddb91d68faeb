const http = require('http');
const socketIo = require('socket.io');

// 创建 HTTP 服务器
const server = http.createServer();

// 创建 Socket.io 实例
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

// 监听连接事件
io.on('connection', (socket) => {
  console.log('客户端已连接:', socket.id);

  // 监听客户端断开连接
  socket.on('disconnect', () => {
    console.log('客户端已断开连接:', socket.id);
  });

  // 监听自定义事件
  socket.on('message', (data) => {
    console.log('收到客户端消息:', data);
  });
});

// 定时发送测试消息
setInterval(() => {
  const testMessage = {
    message: `测试通知消息 - ${new Date().toLocaleTimeString()}`,
    data: {
      id: Math.floor(Math.random() * 10000),
      video_name: '重要视频文件',
      event_name: '系统告警'
    }
  };
  
  console.log('发送测试消息:', testMessage);
  io.emit('alert', testMessage);
}, 30000); // 每30秒发送一次测试消息

// 启动服务器
const PORT = 5000;
server.listen(PORT, () => {
  console.log(`WebSocket 测试服务器运行在端口 ${PORT}`);
  console.log(`可以通过以下地址连接:`);
  console.log(`- ws://localhost:${PORT}`);
  console.log(`- ws://*************:${PORT}`);
  
  // 5秒后发送第一条测试消息
  setTimeout(() => {
    const welcomeMessage = {
      message: '欢迎连接到 WebSocket 服务器！',
      data: {
        id: 1001,
        video_name: '欢迎视频',
        event_name: '连接成功'
      }
    };
    
    console.log('发送欢迎消息:', welcomeMessage);
    io.emit('alert', welcomeMessage);
  }, 5000);
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n正在关闭服务器...');
  server.close(() => {
    console.log('服务器已关闭');
    process.exit(0);
  });
});
