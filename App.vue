<script>
import Vue from 'vue'
import {
	mapMutations
} from 'vuex'
import {
	version
} from './package.json'
import checkUpdate from '@/uni_modules/uni-upgrade-center-app/utils/check-update';
// import 'https://cdn.socket.io/4.7.2/socket.io.min.js'
export default {
	onLaunch: function () {
		// #ifdef H5
		console.log(
			`%c hello uniapp %c v${version} `,
			'background:#35495e ; padding: 1px; border-radius: 3px 0 0 3px;  color: #fff',
			'background:#007aff ;padding: 1px; border-radius: 0 3px 3px 0;  color: #fff; font-weight: bold;'
		)
		// #endif
		// 线上示例使用
		// console.log('%c uni-app官方团队诚邀优秀前端工程师加盟，一起打造更卓越的uni-app & uniCloud，欢迎投递简历到 <EMAIL>', 'color: red');
		console.log('App Launch');
		// #ifdef APP-PLUS
		// App平台检测升级，服务端代码是通过uniCloud的云函数实现的，详情可参考：https://ext.dcloud.net.cn/plugin?id=4542
		if (plus.runtime.appid !== 'HBuilder') { // 真机运行不需要检查更新，真机运行时appid固定为'HBuilder'，这是调试基座的appid
			checkUpdate()
		}

		// 请求通知权限
		setTimeout(() => {
			this.requestNotificationPermission();
		}, 1000);

		// 一键登录预登陆，可以显著提高登录速度
		// uni.preLogin({
		// 	provider: 'univerify',
		// 	success: (res) => {
		// 		// 成功
		// 		this.setUniverifyErrorMsg();
		// 		console.log("preLogin success: ", res);
		// 	},
		// 	fail: (res) => {
		// 		this.setUniverifyLogin(false);
		// 		this.setUniverifyErrorMsg(res.errMsg);
		// 		// 失败
		// 		console.log("preLogin fail res: ", res);
		// 	}
		// })
		// #endif

		uni.getSystemInfo({
			success: function (e) {
				// #ifndef MP
				Vue.prototype.StatusBar = e.statusBarHeight;
				if (e.platform == 'android') {
					Vue.prototype.CustomBar = e.statusBarHeight + 50;
				} else {
					Vue.prototype.CustomBar = e.statusBarHeight + 45;
				};
				// #endif

				// #ifdef MP-WEIXIN
				Vue.prototype.StatusBar = e.statusBarHeight;
				let custom = wx.getMenuButtonBoundingClientRect();
				Vue.prototype.Custom = custom;
				Vue.prototype.CustomBar = custom.bottom + custom.top - e.statusBarHeight + 4;
				// #endif		

				// #ifdef MP-ALIPAY
				Vue.prototype.StatusBar = e.statusBarHeight;
				Vue.prototype.CustomBar = e.statusBarHeight + e.titleBarHeight;
				// #endif
			}
		});
	},
	onShow: function () {
		console.log('App Show');

		// #ifdef APP-PLUS
		// App端：等待 plus 对象完全初始化后再连接 WebSocket
		if (plus) {
			// plus 已经可用，直接初始化
			this.initSocket();
		} else {
			// 等待 plus 对象初始化
			document.addEventListener('plusready', () => {
				console.log('plus 对象已准备就绪');
				this.initSocket();
			});
		}
		// #endif

		// #ifndef APP-PLUS
		// 非App端：直接初始化 WebSocket
		this.initSocket();
		// #endif
	},

	onHide: function () {
		console.log('App Hide');

		// 停止心跳
		this.stopHeartbeat();

		// 关闭WebSocket连接
		if (this.socketTask) {
			try {
				this.socketTask.close();
			} catch (e) {
				console.error('关闭WebSocket连接失败:', e);
			}
		}
	},
	globalData: {
		test: ''
	},
	methods: {
		//提醒函数
		// #ifdef APP-PLUS
		/* === 本地通知栏 + 震动 + 音效 === */
		showLocalNotification(title, content, data = {}) {
			try {
				// 检查 plus.notification 是否可用
				if (plus && plus.notification) {
					console.log('创建系统通知:', title, content);

					// 创建本地通知
					const notification = plus.notification.create({
						title: title,
						content: content,
						icon: '/static/logo.png',  // 通知图标
						sound: 'system',           // 系统提示音
						vibrate: true,             // 震动
						when: new Date(),          // 通知时间
						cover: false,              // 是否覆盖之前的通知
						extra: data                // 额外数据，点击时可以获取
					});

					// 显示通知
					notification.show();

					console.log('系统通知已显示');

					// 监听通知点击事件
					notification.addEventListener('click', (event) => {
						console.log('用户点击了通知:', event);

						// 获取额外数据
						const extraData = event.payload || data;
						const { id, video_name, event_name } = extraData;

						// 打开应用并跳转到指定页面
						if (id) {
							uni.navigateTo({
								url: `/pages/views/index/VideoDetail?id=${id}&video_name=${encodeURIComponent(video_name || '')}&event_name=${encodeURIComponent(event_name || '')}`
							});
						}
					});

				} else {
					console.warn('plus.notification 不可用，使用降级方案');
					this.showFallbackNotification(title, content, data);
				}
			} catch (error) {
				console.error('显示通知失败:', error);
				this.showFallbackNotification(title, content, data);
			}
		},

		// 降级通知方案
		showFallbackNotification(title, content, data = {}) {
			// 使用 uni.showModal 作为降级方案
			uni.showModal({
				title: title,
				content: content,
				showCancel: false,
				confirmText: '查看详情',
				success: (res) => {
					if (res.confirm && data.id) {
						const { id, video_name, event_name } = data;
						uni.navigateTo({
							url: `/pages/views/index/VideoDetail?id=${id}&video_name=${encodeURIComponent(video_name || '')}&event_name=${encodeURIComponent(event_name || '')}`
						});
					}
				}
			});
		},

		playLocalSound(file = '_www/static/alert.mp3') {
			try {
				if (plus && plus.audio && plus.audio.createPlayer) {
					plus.audio.createPlayer(file).play();
				} else {
					console.warn('plus.audio 不可用');
				}
			} catch (error) {
				console.error('播放音效失败:', error);
			}
		},

		vibrate() {
			try {
				if (plus && plus.device && plus.device.vibrate) {
					plus.device.vibrate(500);
				} else {
					console.warn('plus.device.vibrate 不可用');
				}
			} catch (error) {
				console.error('震动失败:', error);
			}
		},

		// 安全的App端通知处理方法
		handleAppNotification(message, data = {}) {
			console.log('处理App端通知:', message, data);

			// 1. 显示系统通知栏通知（传递数据）
			this.showLocalNotification('新消息', message, data);

			// 2. 震动
			this.vibrate();

			// 3. 播放音效
			this.playLocalSound();
		},

		// 请求通知权限
		requestNotificationPermission() {
			try {
				if (plus && plus.navigator && plus.navigator.requestPermission) {
					plus.navigator.requestPermission(
						'NOTIFICATION',
						(result) => {
							console.log('通知权限请求结果:', result);
							if (result.granted) {
								console.log('通知权限已授予');
							} else {
								console.log('通知权限被拒绝');
								uni.showToast({
									title: '请在设置中开启通知权限',
									icon: 'none',
									duration: 3000
								});
							}
						},
						(error) => {
							console.error('请求通知权限失败:', error);
						}
					);
				}
			} catch (error) {
				console.error('请求权限时出错:', error);
			}
		},

		triggerAlert(data) {
		  this.showLocalNotification('新消息', data.data || '您有一条新消息');
		  this.vibrate();
		  this.playLocalSound();
		},
		// #endif
		
		
		//
		...mapMutations(['setUniverifyErrorMsg', 'setUniverifyLogin']),

		initSocket() {
			console.log('初始化 WebSocket 连接');

			// 关闭已存在的连接
			if (this.socketTask) {
				try {
					this.socketTask.close();
				} catch (e) {
					console.error('关闭旧连接失败:', e);
				}
			}

			// 服务器地址 - 根据需要选择一个
			const serverUrl = 'ws://192.168.1.124:5000/ws/push_event';
			// const serverUrl = 'ws://101.43.246.156:5000';

			// 使用 uni-app 的 WebSocket API
			this.socketTask = uni.connectSocket({
				url: serverUrl,
				success: () => {
					console.log('WebSocket 连接请求已发送');
				},
				fail: (error) => {
					console.error('WebSocket 连接请求失败:', error);
					// 尝试重连
					setTimeout(() => {
						this.initSocket();
					}, 3000);
				}
			});

			// 监听 WebSocket 连接打开
			this.socketTask.onOpen(() => {
				console.log('WebSocket 连接已打开');

				// 发送 Socket.io 握手消息
				this.socketTask.send({
					data: '40', // Engine.IO v4 握手
					success: () => {
						console.log('握手消息发送成功');

						// 发送连接消息
						setTimeout(() => {
							this.socketTask.send({
								data: '42["connection",{}]',
								success: () => console.log('连接消息发送成功')
							});
						}, 100);
					}
				});

				// 设置心跳保持连接
				this.startHeartbeat();
			});

			// 监听 WebSocket 接收到服务器的消息
			this.socketTask.onMessage((res) => {
				console.log('收到消息:', res.data);

				const data = res.data;
				
				
				// const jsonObject = JSON.parse(data);
				
				// console.log(jsonObject, '1111111112222333');
// console.log(typeof data, '191-----')
				// 处理 Socket.io 消息
				if (typeof data === 'string') {
					
					// console.log(typeof data, '195-----')
					// console.log(data)
					// 心跳响应
					if (data === '3') {
						console.log('收到心跳响应');
						return;
					}

					// 事件消息: 42["event_name", payload]
					// if (data.startsWith('42')) {
						try {
							// const messageContent = data.substring(2);
							const parsed = JSON.parse(data);
							
							console.log(parsed, '209------------')

							// 处理接收到的消息
							if (parsed && parsed.message) {
								const message = parsed.message;
								const eventData = parsed.data || {};
								const { id, video_name, event_name } = eventData;

								// #ifdef APP-PLUS
								// App端: 使用安全的通知方法
								this.handleAppNotification(message, { id, video_name, event_name });
								// #endif

								// #ifndef APP-PLUS
								// 非App端: 弹窗
								uni.showModal({
									title: '新消息',
									content: message,
									showCancel: false,
									confirmText: '去看看',
									success: (res) => {
										if (res.confirm && id) {
											uni.navigateTo({
												url: `/pages/views/index/VideoDetail?id=${id}&video_name=${encodeURIComponent(video_name || '')}&event_name=${encodeURIComponent(event_name || '')}`
											});
										}
									}
								});
								// #endif
							}
						} catch (e) {
							console.error('解析消息失败:', e);
						}
					// }
				}
			});

			// 监听 WebSocket 错误
			this.socketTask.onError((res) => {
				console.error('WebSocket 错误:', res);
			});

			// 监听 WebSocket 关闭
			this.socketTask.onClose((res) => {
				console.log('WebSocket 已关闭:', res);

				// 清除心跳定时器
				this.stopHeartbeat();

				// 自动重连
				// setTimeout(() => {
				// 	console.log('尝试重新连接...');
				// 	this.initSocket();
				// }, 3000);
			});

			// 保存 socketTask 到全局
			this.$socket = this.socketTask;
		},

		// 开始发送心跳
		startHeartbeat() {
			// 清除可能存在的旧定时器
			this.stopHeartbeat();

			// 设置新的心跳定时器 (25秒发送一次)
			this.heartbeatTimer = setInterval(() => {
				if (this.socketTask && this.socketTask.readyState === 1) {
					console.log('发送心跳');
					this.socketTask.send({
						data: '2', // Engine.IO v4 心跳
						fail: (err) => {
							console.error('心跳发送失败:', err);
							this.stopHeartbeat();
							// 尝试重连
							this.initSocket();
						}
					});
				}
			}, 25000);
		},

		// 停止心跳
		stopHeartbeat() {
			if (this.heartbeatTimer) {
				clearInterval(this.heartbeatTimer);
				this.heartbeatTimer = null;
			}
		},
	}
}
</script>

<style lang="scss">
@import "colorui/main.css";
@import "colorui/icon.css";
@import "uview-ui/theme.scss";
@import "uview-ui/index.scss";
// @import '@/uni_modules/uni-scss/index.scss';
/* #ifndef APP-PLUS-NVUE */
/* uni.css - 通用组件、模板样式库，可以当作一套ui库应用 */
@import './common/uni.css';
// @import '@/static/customicons.css';
/* H5 兼容 pc 所需 */
/* #ifdef H5 */
@media screen and (min-width: 768px) {
	body {
		overflow-y: scroll;
	}
}

/* 顶栏通栏样式 */
/* .uni-top-window {
	    left: 0;
	    right: 0;
	} */

uni-page-body {
	background-color: #FFFFFF !important;
	overflow-y: scroll;
	// min-height: 100% !important;
	// height: auto !important;
}

.uni-top-window uni-tabbar .uni-tabbar {
	background-color: #fff !important;
}

.uni-app--showleftwindow .hideOnPc {
	display: none !important;
}

/* #endif */

/* 以下样式用于 hello uni-app 演示所需 */
page {
	background-color: #efeff4;
	height: 100%;
	font-size: 28rpx;
	/* line-height: 1.8; */
}

.fix-pc-padding {
	padding: 0 50px;
}

.uni-header-logo {
	padding: 30rpx;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	margin-top: 10rpx;
}

.uni-header-image {
	width: 100px;
	height: 100px;
}

.uni-hello-text {
	color: #7A7E83;
}

.uni-hello-addfile {
	text-align: center;
	line-height: 300rpx;
	background: #FFF;
	padding: 50rpx;
	margin-top: 10px;
	font-size: 38rpx;
	color: #808080;
}

/* #endif*/
</style>
