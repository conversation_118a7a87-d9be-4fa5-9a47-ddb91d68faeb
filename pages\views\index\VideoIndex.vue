<template>
  <view class="container">
    <!-- 状态栏 -->
    <view class="background1">
      <view class="status-bar">
        <!-- <text class="time">9:41</text>
      <view class="status-icons">
        <text class="signal">●●●●</text>
        <text class="wifi">📶</text>
        <view class="battery">🔋</view>
      </view> -->
      </view>

      <!-- 头部导航 -->
      <view class="header">
        <!-- <view class="nav-bar">
        <text class="back-btn" @click="goBack"></text>
        <text class="title">查询</text>
        <view class="placeholder"></view>
      </view> -->
        <view class="navbar">
          <!-- <view class="nav-left">
	      <button class="back-btn">
	      </button>
	    </view> -->
          <view class="nav-title">视频首页</view>
          <view class="nav-right" />
        </view>

        <!-- 搜索框 -->
        <view class="search-container">
          <view class="search-box">
            <!-- <text class="search-icon">🔍</text> -->
            <input class="search-input" placeholder="搜索" v-model="searchText" @input="onSearchInput" />
          </view>
        </view>
      </view>
    </view>


    <!-- 筛选器 -->
    <view class="filter-container">
      <view class="filter-wrapper">
        <view class="filter-item" :class="{ active: showCategoryDropdown }" @click="toggleCategoryDropdown">
          <text class="filter-text">{{ selectedCategory.name }}</text>
          <text class="filter-arrow" :class="{ rotate: showCategoryDropdown }">▼</text>
        </view>

        <!-- 事件分类下拉框 -->
        <view v-if="showCategoryDropdown" class="dropdown-overlay" @click="closeCategoryDropdown">
          <view class="dropdown-menu category-dropdown" @click.stop>
            <view class="dropdown-header">
              <text class="dropdown-title">选择事件分类</text>
            </view>
            <scroll-view class="dropdown-content" scroll-y="true">
              <view v-for="(item, index) in categoryList" :key="index" class="dropdown-item"
                :class="{ selected: selectedCategory === item }" @click="selectCategory(item)">
                <text class="dropdown-item-text">{{ item.name }}</text>
                <text v-if="selectedCategory === item" class="check-icon">✓</text>
              </view>
            </scroll-view>
          </view>
        </view>
      </view>

      <view class="filter-wrapper">
        <view class="filter-item" :class="{ active: showRegionDropdown }" @click="toggleRegionDropdown">
          <text class="filter-text">{{ selectedRegion.label }}</text>
          <text class="filter-arrow" :class="{ rotate: showRegionDropdown }">▼</text>
        </view>

        <!-- 地区下拉框 -->
        <view v-if="showRegionDropdown" class="dropdown-overlay" @click="closeRegionDropdown">
          <view class="dropdown-menu region-dropdown" @click.stop>
            <view class="dropdown-header">
              <text class="dropdown-title">选择地区</text>
            </view>
            <scroll-view class="dropdown-content" scroll-y="true">
              <view v-for="(item, index) in regionList" :key="index" class="dropdown-item"
                :class="{ selected: selectedRegion === item }" @click="selectRegion(item)">
                <text class="dropdown-item-text">{{ item.label }}</text>
                <text v-if="selectedRegion === item" class="check-icon">✓</text>
              </view>
            </scroll-view>
          </view>
        </view>
      </view>

      <!-- <view class="filter-wrapper">
        <view class="filter-item" :class="{ active: showAccountDropdown }" @click="toggleAccountDropdown">
          <text class="filter-text">{{ selectedAccount.name }}</text>
          <text class="filter-arrow" :class="{ rotate: showAccountDropdown }">▼</text>
        </view>

        账号下拉框
        <view v-if="showAccountDropdown" class="dropdown-overlay" @click="closeAccountDropdown">
          <view class="dropdown-menu account-dropdown" @click.stop>
            <view class="dropdown-header">
              <text class="dropdown-title">选择账号类型</text>
            </view>
            <scroll-view class="dropdown-content" scroll-y="true">
              <view v-for="(item, index) in accountList" :key="index" class="dropdown-item"
                :class="{ selected: selectedAccount === item }" @click="selectAccount(item)">
                <text class="dropdown-item-text">{{ item.name }}</text>
                <text v-if="selectedAccount === item" class="check-icon">✓</text>
              </view>
            </scroll-view>
          </view>
        </view>
      </view> -->

      <view class="filter-wrapper">
        <view class="filter-item" :class="{ active: showUrgencyDropdown }" @click="toggleUrgencyDropdown">
          <text class="filter-text">{{ selectedUrgency.name }}</text>
          <text class="filter-arrow" :class="{ rotate: showUrgencyDropdown }">▼</text>
        </view>

        <!-- 紧急程度下拉框 -->
        <view v-if="showUrgencyDropdown" class="dropdown-overlay" @click="closeUrgencyDropdown">
          <view class="dropdown-menu account-dropdown" @click.stop>
            <view class="dropdown-header">
              <text class="dropdown-title">选择紧急程度</text>
            </view>
            <scroll-view class="dropdown-content" scroll-y="true">
              <view v-for="(item, index) in urgencyList" :key="index" class="dropdown-item"
                :class="{ selected: selectedUrgency === item }" @click="selectUrgency(item)">
                <text class="dropdown-item-text">{{ item.name }}</text>
                <text v-if="selectedUrgency === item" class="check-icon">✓</text>
              </view>
            </scroll-view>
          </view>
        </view>
      </view>


      <view class="filter-wrapper">
        <view class="filter-item" :class="{ active: showTimeDropdown }" @click="toggleTimeDropdown">
          <text class="filter-text">{{ selectedTime.name }}</text>
          <text class="filter-arrow" :class="{ rotate: showTimeDropdown }">▼</text>
        </view>

        <!-- 时间下拉框 -->
        <view v-if="showTimeDropdown" class="dropdown-overlay" @click="closeTimeDropdown">
          <view class="dropdown-menu account-dropdown" @click.stop>
            <view class="dropdown-header">
              <text class="dropdown-title">选择发布时间</text>
            </view>
            <scroll-view class="dropdown-content" scroll-y="true">
              <view v-for="(item, index) in timelist" :key="index" class="dropdown-item"
                :class="{ selected: selectedTime === item }" @click="selectTime(item)">
                <text class="dropdown-item-text">{{ item.name }}</text>
                <text v-if="selectedTime === item" class="check-icon">✓</text>
              </view>
            </scroll-view>
          </view>
        </view>
      </view>


      <!-- <view class="filter-wrapper">
        <view class="filter-item" :class="{ active: showMoreDropdown }" @click="toggleMoreDropdown">
          <text class="filter-text">更多</text>
          <text class="filter-arrow" :class="{ rotate: showMoreDropdown }">▼</text>
        </view> -->

        <!-- 高级筛选表单 -->
        <!-- <view v-if="showMoreDropdown" class="dropdown-overlay" @click="closeMoreDropdown">
          <view class="dropdown-menu more-dropdown" @click.stop>
            <view class="dropdown-header">
              <text class="dropdown-title">高级筛选</text>
            </view>
            <view class="dropdown-content">
              <view class="form-group">
                <text class="form-label">条件1</text>
                <input v-model="advancedFilters.condition1" class="form-input" />
              </view>
              <view class="form-group">
                <text class="form-label">条件2</text>
                <input v-model="advancedFilters.condition2" class="form-input" />
              </view>
              <view class="form-group">
                <text class="form-label">条件3</text>
                <input v-model="advancedFilters.condition3" class="form-input" />
              </view>
              <view class="form-buttons">
                <button @click="applyAdvancedFilters">应用筛选</button>
                <button @click="resetAdvancedFilters">重置</button>
              </view>
            </view>
          </view>
        </view> -->

        <!-- <view>
          <uni-popup ref="popup" background-color="#fff" @change="change">
            <view class="popup-content" :class="{ 'popup-height': type === 'left' || type === 'right' }"><text
                class="text">popup 内容</text></view>
          </uni-popup>
        </view> -->
        <!-- <view>
          <uni-popup ref="popup" background-color="#fff" @change="change">
            <view class="popup-content">
            </view>
          </uni-popup>
        </view>
        <view class="example-body box">
          <button class="button" type="primary" @click="toggle('bottom')">
            <view class="detail-label">地点</view>
          </button>
        </view>

      </view> -->




    </view>

    <!-- 内容列表 -->
    <scroll-view class="content-list" scroll-y="true">
      <view class="news-item" v-for="(item, index) in filteredNewsList" :key="index" @click="goToVideoDetail(item.id)">
        <view class="news-content">
          <image v-if="item.image" class="news-image" :src="item.image" mode="aspectFill" />
          <view class="news-text">
            <text class="news-title">{{ item.title }}</text>
            <view v-if="item.author" class="author-info">
              <image class="author-avatar" :src="item.author.avatar" mode="aspectFill" />
              <text class="author-name">{{ item.author.name }}</text>
              <text class="follower-count">粉丝数 {{ item.author.followers }}</text>
            </view>
          </view>
        </view>

        <view class="stats-container">
          <view class="stat-item">
            <text class="stat-number">{{ item.likes }}</text>
            <text class="stat-label">点赞数</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ item.comments }}</text>
            <text class="stat-label">评论数</text>
          </view>
          <!-- <view class="stat-item">
            <text class="stat-number1">{{ item.account }}</text>
            <text class="stat-label">发布账号</text>
          </view> -->
          <view class="stat-item">
            <text class="stat-number2">{{ item.timeRange }}</text>
            <text class="stat-label">发布时间</text>
          </view>
          <view class="stat-item">
            <text class="stat-number3">{{ item.event_urgency }}</text>
            <text class="stat-label">紧急程度</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
// import uni from 'uni-app'; // Import uni variable
import { yqsjfl, yqdqlb, yqzhlb, yqsplb, yqsjlb,yqsjjjcd } from "@/api/phgl.js";


export default {
  data() {
    return {
      djcs: 0,
      searchText: '',
      selectedCategory: { name: '分类', value: '' },
      selectedRegion: { label: '地区', value: '' },
      selectedAccount: { name: '账号', value: '' },
      selectedTime: { name: '时间', value: '' },
      selectedUrgency: { name: '紧急程度', value: '' },
      showCategoryDropdown: false,
      showRegionDropdown: false,
      showAccountDropdown: false,
      showTimeDropdown: false,
      showUrgencyDropdown: false,
      // categoryList: ['事件分类', '突发事件', '天气预警', '交通事故', '社会新闻', '政务公告', '民生服务'],
      // regionList: ['地区', '哈尔滨', '黑龙江', '东北地区', '全国', '大庆', '齐齐哈尔', '牡丹江'],
      // accountList: ['账号', '官方账号', '个人账号', '媒体账号', '认证账号', '企业账号', '政府账号'],
      categoryList: [],
      regionList: [],
      accountList: [],
      totalList: [],
      timelist: [],
      urgencyList: [],

      showMoreDropdown: false,
      advancedFilters: {
        condition1: '',
        condition2: '',
        condition3: '',
        // 更多条件...
      },
      bottom: 'bottom',

      newsList: [
        // {
        //   title: '哈尔滨全域大风蓝色预警！下班快回家，能不出门就别出门了',
        //   likes: '4.9W',
        //   comments: '163.9W',
        //   account: '2678.3W',
        //   category: '天气预警',
        //   region: '哈尔滨'
        // },
        // {
        //   title: '水中活化石，达氏鳇再现黑龙江！鱼龄19，体重超过100斤',
        //   image: '/static/fish.jpg',
        //   author: {
        //     name: '你会脸红吗',
        //     avatar: '/static/avatar1.jpg',
        //     followers: '2,065'
        //   },
        //   likes: '3.8W',
        //   comments: '112.9W',
        //   account: '6678.5W',
        //   category: '社会新闻',
        //   region: '黑龙江'
        // },
        // {
        //   title: '突发！市中心商场发生火灾，消防部门紧急救援，现场情况牵动人心',
        //   image: '/static/fire.jpg',
        //   author: {
        //     name: '飞翔的人们',
        //     avatar: '/static/avatar2.jpg',
        //     followers: '1,025'
        //   },
        //   likes: '4.9W',
        //   comments: '163.9W',
        //   account: '2678.3W',
        //   category: '突发事件',
        //   region: '哈尔滨'
        // },
        // {
        //   title: '中央气象台发布暴雨蓝色预警：涉及黑龙江东南部等地',
        //   likes: '4.9W',
        //   comments: '163.9W',
        //   account: '2678.3W',
        //   category: '天气预警',
        //   region: '黑龙江'
        // },
        // {
        //   title: '快加入这块冒险吧#互动游戏#创意',
        //   likes: '4.9W',
        //   comments: '163.9W',
        //   account: '2678.3W',
        //   category: '社会新闻',
        //   region: '全国'
        // }
      ]
    }
  },
  // computed: {
  //   filteredNewsList() {
  //     let filtered = this.newsList;

  //     if (this.searchText) {
  //       filtered = filtered.filter(item =>
  //         item.title.includes(this.searchText)
  //       );
  //     }

  //     if (this.selectedCategory.name !== '事件分类') {
  //       filtered = filtered.filter(item =>
  //         item.category === this.selectedCategory.name
  //       );
  //     }

  //     if (this.selectedRegion !== '地区') {
  //       filtered = filtered.filter(item =>
  //         item.region === this.selectedRegion.name
  //       );
  //     }

  //     return filtered;
  //   }
  // },

  methods: {
    //更多，复杂筛选底部弹框
    change(e) {
      console.log('当前模式：' + e.type + ',状态：' + e.show);
    },
    toggle(center) {
      this.center = center;
      // this.$refs.popup.open(center);
      this.$refs.popup.open(this.center);
    },
    openPopup() {
      this.$refs.popup.open();
    },



    goBack() {
      uni.navigateBack();
    },
    onSearchInput(e) {
      this.searchText = e.detail.value;
      this.fetchVideoList();
    },

    // 事件分类相关方法
    toggleCategoryDropdown() {
      this.showCategoryDropdown = !this.showCategoryDropdown;
      this.showRegionDropdown = false;
      this.showAccountDropdown = false;
    },
    closeCategoryDropdown() {
      this.showCategoryDropdown = false;
    },
    selectCategory(category) {
      this.selectedCategory = category;
      this.showCategoryDropdown = false;
      this.fetchVideoList();
    },

    // 地区相关方法
    // toggleRegionDropdown() {
    //   this.showRegionDropdown = !this.showRegionDropdown;
    //   if (this.showRegionDropdown == true) {
    //     this.regionList = this.totalList.province
    //   }
    //   this.showCategoryDropdown = false;
    //   this.showAccountDropdown = false;
    // },
    // closeRegionDropdown() {
    //   this.showRegionDropdown = false;
    //   if (this.showRegionDropdown == false) {
    //     this.djcs = 0;
    //   }
    // },
    // selectRegion(region) {
    //   this.djcs++;
    //   console.log(this.djcs, '点击次数');

    //   this.selectedRegion = region;
    //   if (this.djcs == 1) {
    //     this.regionList = this.totalList.city[11]
    //   } else if (this.djcs == 2) {
    //     this.regionList = this.totalList.county[this.selectedRegion.value]
    //   }
    //   // this.showRegionDropdown = false;
    //   this.fetchVideoList();
    // },
    toggleRegionDropdown() {
      this.showRegionDropdown = !this.showRegionDropdown;
      if (this.showRegionDropdown) {
        this.regionList = this.totalList.province;
        this.showCategoryDropdown = false;
        this.showAccountDropdown = false;
      } else {
        this.djcs = 0;
      }
    },
    closeRegionDropdown() {
      this.showRegionDropdown = false;
      this.djcs = 0;
    },
    selectRegion(region) {
      this.djcs++;
      console.log(this.djcs, '点击次数');

      this.selectedRegion = region;
      if (this.djcs == 1) {
        this.regionList = this.totalList.city[region.value];
      } else if (this.djcs == 2) {
        this.regionList = this.totalList.county[region.value];
      }
      this.fetchVideoList();
    },

    // 账号相关方法
    toggleAccountDropdown() {
      this.showAccountDropdown = !this.showAccountDropdown;
      this.showCategoryDropdown = false;
      this.showRegionDropdown = false;
    },
    closeAccountDropdown() {
      this.showAccountDropdown = false;
    },
    selectAccount(account) {
      this.selectedAccount = account;
      this.showAccountDropdown = false;
      this.fetchVideoList();
    },
    // 时间相关方法
    toggleTimeDropdown() {
      this.showTimeDropdown = !this.showTimeDropdown;
      this.showCategoryDropdown = false;
      this.showRegionDropdown = false;
      this.showAccountDropdown = false;
    },
    closeTimeDropdown() {
      this.showTimeDropdown = false;
    },
    selectTime(timeRange) {
      this.selectedTime = timeRange;
      this.showTimeDropdown = false;
      this.fetchVideoList();
    },

    //紧急程度相关方法
    toggleUrgencyDropdown() {
      this.showUrgencyDropdown = !this.showUrgencyDropdown;
      this.showCategoryDropdown = false;
      this.showRegionDropdown = false;
      this.showAccountDropdown = false;
    },
    closeUrgencyDropdown() {
      this.showUrgencyDropdown = false;
    },
    selectUrgency(event_urgency) {
      this.selectedUrgency = event_urgency;
      this.showUrgencyDropdown = false;
      this.fetchVideoList();
    },


    // 更多筛选条件相关方法
    toggleMoreDropdown() {
      this.showMoreDropdown = !this.showMoreDropdown;
    },
    applyAdvancedFilters() {
      this.showMoreDropdown = false;
      this.fetchVideoList(); // 根据高级筛选条件重新获取视频列表
    },
    resetAdvancedFilters() {
      this.advancedFilters = {
        condition1: '',
        condition2: '',
        condition3: '',
        // 更多条件...
      };
    },

    //视频详情跳转
    // goToVideoDetail(id) {
    //   this.$router.push({
    //     path: "/pages/views/index/VideoDetail",
    //     query: { id: id }
    //   });
    // },
	  goToVideoDetail(id) {
	    uni.navigateTo({
	      url: `/pages/views/index/VideoDetail?id=${id}`
	    });
	  },

    // 获取事件分类数据
    async fetchEventClassifications() {
      try {
        const response = await yqsjfl();
        if (response.data.code === 10000) {
          this.categoryList = response.data.data.map(item => ({
            name: item.name,
            value: item.value
          }));
        } else {
          console.error('Failed to fetch event classifications:', response.data.message);
        }
      } catch (error) {
        console.error('Error fetching event classifications:', error);
      }
    },

    // 获取地区数据
    async fetchRegions() {
      try {
        const response = await yqdqlb();

        if (response.data.code === 10000) {
          this.totalList = response.data.data;
          this.regionList = response.data.data.province
          console.log(this.regionList, '地区数据');

        } else {
          console.error('Failed to fetch event classifications:', response.data.message);
        }
      } catch (error) {
        console.error('Error fetching event classifications:', error);
      }
    },

    // 获取账号数据
    async fetchAccounts() {
      try {
        const response = await yqzhlb();
        if (response.data.code === 10000) {
          this.accountList = response.data.data.map(item => ({
            name: item.name,
            value: item.value
          }));
        } else {
          console.error('Failed to fetch event classifications:', response.data.message);
        }
      } catch (error) {
        console.error('Error fetching event classifications:', error);
      }
    },

    //获取发布时间数据
    async fetchTime() {
      try {
        const response = await yqsjlb();
        if (response.data.code === 10000) {
          this.timelist = response.data.data.map(item => ({
            name: item.name,
            value: item.value
          }));
        } else {
          console.error('Failed to fetch event classifications:', response.data.message);
        }
      } catch (error) {
        console.error('Error fetching event classifications:', error);
      }
    },

    //获取紧急程度列表
    async fetchUrgency() {
      try {
        const response = await yqsjjjcd();
        if (response.data.code === 10000) {
          this.urgencyList = response.data.data.map(item => ({
            name: item.name,
            value: item.value
          }));
        } else {
          console.error('Failed to fetch event classifications:', response.data.message);
        }
      } catch (error) {
        console.error('Error fetching event classifications:', error);
      }
    },

    //更多条件
    // async fetchVideoList() {
    //   const params = {
    //     searchName: this.searchText,
    //     eventClassification: this.selectedCategory.value,
    //     areaCity: this.selectedRegion.label,
    //     account: this.selectedAccount.value,
    //     timeRange: this.selectedTime.value,
    //     eventUrgency: this.selectedUrgency.value,
    //     advancedFilters: this.advancedFilters, // 添加高级筛选条件
    //   };

    //   const { data } = await yqsplb(params);

    //   if (data.code === 10000) {
    //     this.newsList = data.data.map(item => ({
    //       id: item.id,
    //       title: item.name,
    //       likes: item.dzs,
    //       comments: item.pls,
    //       account: item.fbzh,
    //       category: item.classification,
    //       region: item.areaCity,
    //       accountType: item.fbzh,
    //       timeRange: item.time,
    //       event_urgency: item.event_urgency
    //     }));
    //   } else {
    //     console.error('Failed to fetch video list:', data.message);
    //   }
    // },


    // 获取视频列表数据
    async fetchVideoList() {
      const params = {
        searchName: this.searchText,
        eventClassification: this.selectedCategory.value,
        areaCity: this.selectedRegion.label,
        account: this.selectedAccount.value,
        timeRange: this.selectedTime.value,
        eventUrgency: this.selectedUrgency.value
      };

      const { data } = await yqsplb(params);

      if (data.code === 10000) {
        this.newsList = data.data.map(item => ({
          id: item.id,
          title: item.name,
          likes: item.dzs,
          comments: item.pls,
          account: item.fbzh,
          //         // 添加以下字段用于筛选
          category: item.classification, // 事件分类
          region: item.areaCity, // 地区
          accountType: item.fbzh, // 账号类型
          timeRange: item.time, // 发布时间
          event_urgency: item.event_urgency // 事件紧急程度

        }));
        console.log(this.newsList, '415');

      } else {
        console.error('Failed to fetch video list:', data.message);
      }
    },

  },
  mounted() {
    this.fetchEventClassifications();
    this.fetchRegions();
    this.fetchAccounts();
    this.fetchVideoList();
    this.fetchTime();
    this.fetchUrgency();

  },
  watch: {

  },
  computed: {
    filteredNewsList() {

      let filtered = this.newsList;
      // console.log(this.searchText, '搜索文本');
      // console.log(this.selectedCategory.value, '选择事件分类');
      // console.log(this.selectedRegion.value, '选择地区');
      // console.log(this.selectedAccount.value, '选择账号类型');

      // // 搜索文本筛选
      // if (this.searchText) {
      //   console.log('搜索文本筛选');
      //   filtered = filtered.filter(item =>
      //     item.title.includes(this.searchText)
      //   );
      // }

      // // 事件分类筛选
      // if (this.selectedCategory.value) {
      //   console.log('事件分类筛选');
      //   filtered = filtered.filter(item =>
      //     item.classification == this.selectedCategory.value
      //   );
      // }

      // // 地区筛选
      // if (this.selectedRegion.value) {
      //   console.log('地区筛选');
      //   filtered = filtered.filter(item =>
      //     item.region == this.selectedRegion.value
      //   );
      // }

      // // 账号类型筛选
      // if (this.selectedAccount.value) {
      //   console.log('账号类型筛选');
      //   filtered = filtered.filter(item =>
      //     item.accountType == this.selectedAccount.value
      //   );
      // }

      return filtered;
    }
  },
}
</script>

<style scoped>
.container {
  /* background-color: #f5f5f5; */
  min-height: 100vh;
  /* background-image: linear-gradient(0deg, #EDF8FF 0%, rgba(190, 221, 249, 0.00) 74%, #0674E3 100%); */
}

.background1 {
  background: url('./img/header-bg.png') no-repeat center/cover;
}

.status-bar {
  /* background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%); */
  height: 44px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  /* padding: 0 20px; */
  color: white;
  font-size: 16px;
  font-weight: 600;
}

.time {
  font-size: 16px;
  font-weight: 600;
}

.status-icons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.signal,
.wifi {
  font-size: 14px;
}

.battery {
  font-size: 16px;
}

/* .header { */
  /* background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%); */
  /* padding-bottom: 20px; */
/* } */

.header {
  padding-bottom: 20px;
  background: url('./img/header-bg.png') no-repeat center/cover;
}

.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  color: white;
}

.back-btn {
  /*  font-size: 28px;
  font-weight: 300;
  width: 30px; */
}

.title {

  font-size: 40rpx;
  /*  font-size: 18px;
  font-weight: 600;
  flex: 1;
  text-align: center; */
}

.placeholder {
  width: 30px;
}

.search-container {
  padding: 0 20px;
}

.search-box {
  background: white;
  border-radius: 25px;
  display: flex;
  align-items: center;
  padding: 12px 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.search-icon {
  color: #999;
  margin-right: 10px;
  font-size: 16px;
}

.search-input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 16px;
  color: #333;
}

.filter-container {
  /* background: white; */
  display: flex;
  /* padding: 15px 0; */
  /* border-bottom: 1px solid #eee; */
  position: relative;
  z-index: 10;
  overflow-x: scroll;
}

.filter-wrapper {
  flex: 1;
  position: relative;
}

.filter-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  padding: 10px;
  transition: all 0.3s ease;
  border-radius: 8px;
  margin: 0 5px;
}

.filter-item.active {
  background-color: #f0f7ff;
  color: #4A90E2;
}

.filter-text {
  font-size: 16px;
  color: #000000;
  transition: color 0.3s ease;
  white-space: nowrap;
}

.filter-item.active .filter-text {
  color: #4A90E2;
  font-weight: 600;
}

.filter-arrow {
  font-size: 12px;
  color: #000000;
  transition: all 0.3s ease;
}

.filter-arrow.rotate {
  transform: rotate(180deg);
  color: #4A90E2;
}

/* 下拉框样式 */
.dropdown-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 1000;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 200px;
  animation: fadeIn 0.3s ease;
}

.dropdown-menu {
  background: white;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  width: 280px;
  max-height: 400px;
  overflow: hidden;
  animation: slideDown 0.3s ease;
  transform-origin: top center;
}

.dropdown-header {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  padding: 16px 20px;
  text-align: center;
}

.dropdown-title {
  color: white;
  font-size: 16px;
  font-weight: 600;
}

.dropdown-content {
  max-height: 300px;
}

.dropdown-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #f5f5f5;
  transition: all 0.2s ease;
  position: relative;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item.selected {
  background-color: #f0f7ff;
  color: #4A90E2;
}

.dropdown-item-text {
  font-size: 15px;
  color: #333;
  flex: 1;
}

.dropdown-item.selected .dropdown-item-text {
  color: #4A90E2;
  font-weight: 600;
}

.check-icon {
  color: #4A90E2;
  font-size: 16px;
  font-weight: bold;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.content-list {
  flex: 1;
  padding: 10px;
  background:#E8F2FE;
}

.news-item {
  background: white;
  border-radius: 12px;
  margin-bottom: 15px;
  padding: 20px;
  /* box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06); */
  box-shadow: 0px 2px 10px 0px rgba(47, 114, 186, 0.11);
  border-radius: 8px;
}

.news-content {
  display: flex;
  margin-bottom: 15px;
}

.news-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  margin-right: 15px;
  flex-shrink: 0;
}

.news-text {
  flex: 1;
}

.news-title {
  font-size: 16px;
  color: #333;
  line-height: 1.4;
  margin-bottom: 10px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.author-avatar {
  width: 20px;
  height: 20px;
  border-radius: 50%;
}

.author-name {
  font-size: 14px;
  color: #666;
}

.follower-count {
  font-size: 12px;
  color: #999;
}

.stats-container {
  display: flex;
  justify-content: space-between;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-number {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.stat-number1 {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: #04aef7;
  margin-bottom: 5px;
}

.stat-number2 {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: #ff8c00;
  margin-bottom: 5px;
  width: 240rpx;
}

.stat-number3 {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: #f93d04;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  color: #999;
}

/* 导航栏样式（复用） */
.navbar {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 40rpx;
  color: white;
  margin-bottom: 40rpx;
}

.nav-title {
  font-size: 40rpx;
  /* font-weight: 600; */
  width: 100%;
  text-align: center;
}

.back-btn {
  background: none;
  border: none;
  padding: 16rpx;
  margin-left: -16rpx;
  cursor: pointer;
  color: white;
  font-size: 32rpx;
}

.back-arrow {
  font-size: 48rpx;
  color: white;
  font-weight: bold;
}

	.popup-content {
		@include flex;
		align-items: center;
		justify-content: center;
		padding: 7px;
		height: 50px;
		background-color: #fff;
	}

	.text {
		font-size: 12px;
		color: #333;
	}

	.dialog,
	.share {
		display: flex;
		flex-direction: column;
	}

	.dialog-box {
		padding: 10px;
	}

	.dialog .button,
	.share .button {
		width: 100%;
		margin: 0;
		margin-top: 10px;
		padding: 3px 0;
		flex: 1;
	}

	.dialog-text {
		font-size: 14px;
		color: #333;
	}
</style>