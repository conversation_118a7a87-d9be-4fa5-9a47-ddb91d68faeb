<template>
  <view class="app">
    <!-- <cu-custom bgColor="common_header" :isBack="true">
	        <block slot="content">文章详情</block>
	      </cu-custom> -->
    <!-- 状态栏 -->
    <view class="background1">
      <view class="status-bar">
        <!-- <view class="status-left">9:41</view>
      <view class="status-right">
        <view class="signal-icons">
          <view class="signal-bars">
            <text class="bar" />
            <text class="bar" />
            <text class="bar" />
            <text class="bar" />
          </view>
          <view class="wifi-icon">📶</view>
          <view class="battery-icon">
            <view class="battery-body">
              <view class="battery-level" />
            </view>
            <view class="battery-tip" />
          </view>
        </view>
      </view> -->
      </view>


      <!-- 导航栏 -->
      <view class="navbar">
        <view class="nav-left">
          <!--        <view class="back-btn" @click="goBack">
            <image src="./img/left.png" style="width: 120rpx; height: 80rpx; margin-top: 36rpx; margin-left: -56rpx;" />
          </view> -->
        </view>
        <view class="nav-title">视频详情</view>
        <view class="nav-right" />
      </view>
      <!-- <view class="navbar"> -->
      <!-- <view class="nav-left" @click="goBack">
          <view class="back-icon"></view>
        </view> -->
      <!--       <view class="nav-title">视频详情</view>
        <view class="nav-right" />
      </view> -->
	  


      <!-- 内容区域 -->

      <!-- 视频信息卡片 -->
      <view class="video-info-card">
        <view class="video-thumbnail">
          <img :src="indexImage" alt="">
          <!-- <image src="https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=120&h=90&fit=crop" mode="aspectFill">
          </image> -->
        </view>
        <view class="video-content">
          <view class="video-title">
            <!-- {{ videoDetails.name }} -->
            {{ videoDetails.zynr }}
          </view>
          <!-- <button class="copy-btn">复制地址</button> -->
        </view>
      </view>
    </view>

    <view class="content-area">
      <!-- 作者信息卡片 -->
      <view class="author-card">
        <view class="author-main">
          <view class="author-avatar">
            <image src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=60&h=60&fit=crop&crop=face"
              mode="aspectFill"></image>
          </view>
          <view class="author-info">
            <view class="author-header">
              <text class="author-name">{{ videoDetails.fbzh }}</text>
            </view>
            <view class="author-header">
              <view class="verified-badge">
                <text class="check-mark">✓</text>
              </view>
              <text class="author-desc">{{ videoDetails.lybq }}</text>
            </view>

            <view class="author-stats">
              <text class="stat-item">关注 <text class="stat-strong">{{ videoDetails.lygzs }}</text></text>
              <text class="stat-item">粉丝 <text class="stat-strong">{{ videoDetails.lyfs }}</text></text>
              <text class="stat-item">获赞 <text class="stat-strong">{{ videoDetails.lydzs }}</text></text>
            </view>
            <view class="author-meta">
              <text>抖音号：{{ videoDetails.lydyh }}</text>
              <!-- <text class="ip-location">IP属地：{{ videoDetails.IPsd }}</text> -->
            </view>
          </view>
        </view>
      </view>

      <!-- 统计数据 -->
      <view class="stats-grid">
        <view class="stat-item">
          <view class="stat-number">{{ videoDetails.dzs }}</view>
          <view class="stat-label"><image src="./img/点赞数.png" mode="aspectFit" style="width: 50rpx; height: 50rpx;" /></image></view>
          <view class="stat-label">点赞数</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{ videoDetails.pls }}</view>
          <view class="stat-label"><image src="./img/评论数.png" mode="aspectFit" style="width: 50rpx; height: 50rpx;" /></image></view>
          <view class="stat-label">评论数</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{ videoDetails.scs }}</view>
          <view class="stat-label"><image src="./img/收藏数.png" mode="aspectFit" style="width: 50rpx; height: 50rpx;" /></image></view>
          <view class="stat-label">收藏数</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{ videoDetails.fxs }}</view>
          <view class="stat-label"><image src="./img/转发.png" mode="aspectFit" style="width: 50rpx; height: 50rpx;" /></image></view>
          <view class="stat-label">分享数</view>
        </view>
      </view>

      <!-- 视频详细信息 -->
      <view class="video-details">
        <!-- <view class="detail-item">
          <view class="detail-number">{{ videoDetails.sc }}</view>
          <view class="detail-label">时长</view>
        </view> -->
        <!-- <view class="detail-item">
          <view class="detail-number1">{{ videoDetails.dd }}</view>
          <view class="detail-label">地点</view>
        </view> -->

        <!-- <view class="detail-item">
          <view class="detail-number1" :class="{ 'expanded': detailExpanded }"  @click="toggleDetail">{{ videoDetails.dd }}</view>
          <view class="detail-label">地点</view>
        </view> -->

        <!-- <view class="detail-item" @click="showLocationPopup = true; locationPopupContent = videoDetails.dd;">
          <view class="detail-number1">{{ videoDetails.dd }}</view>
          <view class="detail-label">地点</view>
        </view> -->

        <!-- 7.25版 -->
        <view class="detail-item" @click="toggleLocationPopup">
          <view class="detail-number1">{{ videoDetails.dd }}</view>
          <view class="detail-label">地点</view>
        </view>

        <!-- 7.28版 -->
        <!-- <view class="detail-item" @click="openPopup">
          <view class="detail-number1">{{ videoDetails.dd }}</view>
          <view class="detail-label" @click="toggle('center')">地点</view>
        </view>
         -->
      <!-- <view class="example-body box">
        <button class="button" type="primary" @click="toggle('center')"><view class="detail-label">地点</view></button>
      </view> -->


        <view class="detail-item">
          <view class="detail-number2">{{ videoDetails.fbsj }}</view>
          <view class="detail-label">发布时间</view>
        </view>
        <!-- <view class="detail-item">
          <view class="detail-number">152个</view>
          <view class="detail-label">热词</view>
        </view> -->
      </view>

      <!-- 地点弹框 -->
      <!-- <view v-if="showLocationPopup" class="location-popup" catchtouchmove="preventDefault">
        <view class="popup-content">
          <text class="popup-text">{{ locationPopupContent }}</text>
          <button class="popup-close" @click="showLocationPopup = false">关闭</button>
        </view>
      </view> -->

      <!-- 7.25版
      <view v-if="showLocationPopup" class="location-popup" @click="closeLocationPopup">
        <view class="popup-content" @click.stop>
          <text class="popup-text">{{ locationPopupContent }}</text>
          <button class="popup-close" @click="closeLocationPopup">关闭</button>
        </view>
      </view> -->

      <!-- 7.28版 -->
      <view v-if="showLocationPopup" class="location-popup" @click="closeLocationPopup">
        <view class="popup-content" @click.stop>
          <text class="popup-text">{{ locationPopupContent }}</text>
          <!-- <button class="popup-close" @click="closeLocationPopup">关闭</button> -->
        </view>
      </view>

      <!-- 地点弹窗 -->
      <!-- <view>
        <uni-popup ref="popup" background-color="#fff" @change="change">
          <view class="popup-content">
            <view class="detail-number">{{ videoDetails.dd }}</view>
          </view>
        </uni-popup>
      </view> -->

      <!-- 分析标签 -->
      <view class="analysis-tabs">
        <view class="tab-item active">事件分析</view>
        <!-- <view class="tab-item">评论分析</view> -->
      </view>

      <!-- 事件严重程度 -->
      <view class="severity-indicator">
        <view class="severity-icon">
          <image src="./img/danger.png" mode="aspectFit"></image>
        </view>
        <view class="severity-content">
          <text class="severity-title">事件紧急程度</text>
          <text class="severity-level">{{ videoDetails.sjfxDatas.sjcd }}</text>
        </view>
      </view>

      <!-- 事件信息表格 -->
      <view class="event-table">
        <view class="table-row">
          <view class="table-label">事件名称</view>
          <view class="table-value">{{ videoDetails.sjfxDatas.sjmc }}</view>
        </view>
        <view class="table-row">
          <view class="table-label">事件分类</view>
          <view class="table-value">{{ videoDetails.sjfxDatas.sjfl }}</view>
        </view>
        <view class="table-row">
          <view class="table-label">事件发现日期</view>
          <view class="table-value">{{ videoDetails.sjfxDatas.sjfxsj }}</view>
        </view>
        <view class="table-row">
          <view class="table-label">事件解决日期</view>
          <view class="table-value">{{ videoDetails.sjfxDatas.sjjjsj }}</view>
        </view>
        <view class="table-row">
          <view class="table-label">事件归管部门</view>
          <view class="table-value">{{ videoDetails.sjfxDatas.sjgsbm }}</view>
        </view>
      </view>

      <!-- 联系方式 -->
      <view class="contact-section">
        <view class="contact-title">联系方式</view>

        <view class="contact-item">
          <view class="contact-name"></view>
          <view class="contact-phone"></view>
          <view class="contact-dept"></view>
        </view>

        <view class="contact-item">
          <view class="contact-name"></view>
          <view class="contact-phone"></view>
          <view class="contact-dept"></view>
        </view>

        <view class="contact-item">
          <view class="contact-name"></view>
          <view class="contact-phone"></view>
          <view class="contact-dept"></view>
        </view>

        <!-- <view class="contact-item">
          <view class="contact-name">艾雪瑞</view>
          <view class="contact-phone">13351825621</view>
          <view class="contact-dept">哈尔滨公积金信息中心</view>
        </view>

        <view class="contact-item">
          <view class="contact-name">安奇书</view>
          <view class="contact-phone">13351825621</view>
          <view class="contact-dept">哈尔滨人力资源社会保障局中心</view>
        </view>

        <view class="contact-item">
          <view class="contact-name">李明明</view>
          <view class="contact-phone">13351825621</view>
          <view class="contact-dept">哈尔滨人力资源社会保障局中心</view>
        </view> -->
      </view>
    </view>
  </view>
</template>

<script>
// import uni from 'uni-app'; // Import uni variable
import { yqspxq } from "@/api/phgl.js";
// import view from '../../component/view/view.vue';

export default {
  // components: { view },
  name: 'VideoDetail',

  data() {
    return {
      videoId: '',
      videoDetails: {},
      spxqList: [],
      sjfxDatas: [],
      indexImage: '',
      detailExpanded: false, // 控制详情展开状态
      showLocationPopup: false, // 控制弹框显示状态
      locationPopupContent: '', // 弹框中显示的地点内容
      center: 'center',
    };
  },

  // created() {
  //   this.videoId = this.$route.query.id;
  //   this.fetchVideoDetails(this.videoId);
  // },
  
    onLoad(options) {
      this.videoId = options?.id || '';
      if (this.videoId) {
        this.fetchVideoDetails();
      } else {
        uni.showToast({
          title: '缺少视频ID',
          icon: 'none',
        });
      }
    },


  methods: {
    change(e) {
      console.log('当前模式：' + e.type + ',状态：' + e.show);
    },
    toggle(center) {
      this.center = center;
      // this.$refs.popup.open(center);
      this.$refs.popup.open(this.center);
    },
    openPopup() {
      this.$refs.popup.open();
    },
    // preventDefault(event) {
    //   event.preventDefault();
    // },

    // 7.25版
    // toggleLocationPopup() {
    //   this.showLocationPopup = !this.showLocationPopup;
    //   if (this.showLocationPopup) {
    //     this.locationPopupContent = this.videoDetails.dd; // 设置弹框内容
    //   }
    // },
    // closeLocationPopup() {
    //   this.showLocationPopup = false;
    // },

    // 7.28版
    toggleLocationPopup() {
      this.showLocationPopup = !this.showLocationPopup;
      if (this.showLocationPopup) {
        this.locationPopupContent = this.videoDetails.dd; // 设置弹框内容
      }
    },
    closeLocationPopup() {
      this.showLocationPopup = false;
    },

    // goBack() {
    //   uni.navigateBack()
    // },
    goBack() {
      uni.navigateTo({
        url: `/pages/views/index/VideoIndex`
      });
    },

    // async fetchVideoDetails(videoId) {
    //   const data = await yqspxq(videoId);
    //   this.videoDetails = data;
    //   console.log(this.videoDetails);
    // },
    async fetchVideoDetails() {
      const params = {
        id: this.videoId,
      };
      const { data } = await yqspxq(params);
      console.log(data, '11111');
      if (data.code === 10000) {
        this.videoDetails = data.data[0];
        console.log(this.videoDetails, '22222');
        this.indexImage = this.getImageUrl(this.videoDetails.indexImage)
      } else {
        console.error('Failed to fetch video details:', data.message);
      }
    },
    // 获取图片地址
    getImageUrl(base64Data) {
      // 如果已有头部信息，直接返回
      if (base64Data.startsWith('data:image')) {
        console.log('已有头部信息，直接返回');
        return base64Data;
      }
      // 否则拼接标准头部
      return `data:image/png;base64,${base64Data}`;
    },

    // 切换展开状态
    // toggleDetail() {
    //   this.detailExpanded = !this.detailExpanded; 
    //   console.log(this.detailExpanded,"111111111111111111111");
    // },

  }
}
</script>

<style scoped>
.app {
  min-height: 100vh;
  /* background: linear-gradient(to bottom, #4A90E2 0%, #5B9BD5 40%, #E8F4FD 80%, #F5F7FA 100%); */
  padding: 0;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  position: relative;
  background-image: linear-gradient(0deg, #EDF8FF 0%, rgba(190, 221, 249, 0.00) 74%, #0674E3 100%);
  /*  background-image: url('./img/header-bg.png');
  background-size: contain;
  background-repeat: no-repeat; */
}

.background1 {
  background: url('./img/header-bg.png') no-repeat center/cover;
}

/* 状态栏样式（复用） */
.status-bar {
  height: 88rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 40rpx;
  color: white;
  font-size: 32rpx;
  font-weight: 600;
}

.status-right {
  display: flex;
  align-items: center;
}

.signal-icons {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.signal-bars {
  display: flex;
  gap: 4rpx;
  align-items: end;
}

.bar {
  width: 6rpx;
  background: white;
  border-radius: 2rpx;
}

.bar:nth-child(1) {
  height: 8rpx;
}

.bar:nth-child(2) {
  height: 12rpx;
}

.bar:nth-child(3) {
  height: 16rpx;
}

.bar:nth-child(4) {
  height: 20rpx;
}

.battery-icon {
  display: flex;
  align-items: center;
  gap: 2rpx;
}

.battery-body {
  width: 44rpx;
  height: 22rpx;
  border: 2rpx solid white;
  border-radius: 4rpx;
  position: relative;
  background: rgba(255, 255, 255, 0.3);
}

.battery-level {
  width: 80%;
  height: 100%;
  background: white;
  border-radius: 2rpx;
}

.battery-tip {
  width: 4rpx;
  height: 12rpx;
  background: white;
  border-radius: 0 2rpx 2rpx 0;
}

/* 导航栏样式（复用） */
.navbar {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 40rpx;
  color: white;
}

.nav-title {
  font-size: 40rpx;
  margin-left:-84rpx;
  /* font-weight: 600; */
}


.back-btn {
  background: none;
  border: none;
  padding: 16rpx;
  margin-left: -16rpx;
  cursor: pointer;
  color: white;
  font-size: 32rpx;
}

.back-arrow {
  font-size: 48rpx;
  color: white;
  font-weight: bold;
  margin-top: 48rpx;
  width: 10rpx;
  height: 10rpx;
}

/* 内容区域 */
.content-area {
  /* padding: 40rpx; */
  background: #E8F2FE;
}

/* 视频信息卡片 */
.video-info-card {
  /* background: white; */
  border-radius: 16rpx;
  padding: 32rpx;
  /* margin-bottom: 32rpx; */
  display: flex;
  gap: 24rpx;
  /* box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08); */

  background: #FFFFFF;
  box-shadow: 0px 2px 10px 0px rgba(47, 114, 186, 0.11);
  border-radius: 8px;
  margin-left: 32rpx;
  margin-right: 32rpx;
  margin-bottom: 18rpx;
  /* margin-top: 18rpx; */
}

.video-thumbnail {
  width: 160rpx;
  height: 208rpx;
  border-radius: 16rpx;
  overflow: hidden;
  flex-shrink: 0;
}

.video-thumbnail img {
  width: 100%;
  height: 100%;
}

.video-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.video-title {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
  margin-bottom: 24rpx;
}

.copy-btn {
  background: #4A90E2;
  color: white;
  border: none;
  /* border-radius: 40rpx; */
  /* padding: 16rpx 32rpx; */
  font-size: 28rpx;
  align-self: flex-end;
  cursor: pointer;
  background-image: linear-gradient(89deg, #2562EE 0%, #64A9F8 100%);
  box-shadow: 0px 4rpx 20rpx 0px rgba(59, 120, 251, 0.27);
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #FFFFFF;
  letter-spacing: 0;
  text-align: center;
  line-height: 72rpx;
  font-weight: 500;
}

/* 作者信息卡片 */
.author-card {
  background-image: url('./img/center-bg.png');
  border-radius: 32rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(74, 144, 226, 0.3);
  background-size: cover;

  /* box-shadow: 0px 2px 10px 0px rgba(47, 114, 186, 0.11); */
  border-radius: 8px;
  margin-left: 32rpx;
  margin-right: 32rpx;
}

.author-main {
  display: flex;
  gap: 24rpx;
}

.author-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.author-avatar image {
  width: 100%;
  height: 100%;
}

.author-info {
  flex: 1;
}

.author-header {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.author-name {
  font-size: 32rpx;
  font-weight: 600;
}

.verified-badge {
  width: 32rpx;
  height: 32rpx;
  background: #FFD700;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.check-mark {
  color: white;
  font-size: 20rpx;
  font-weight: bold;
}

.author-desc {
  font-size: 24rpx;
  opacity: 0.9;
}

.author-stats {
  display: flex;
  gap: 32rpx;
  margin-bottom: 16rpx;
}

.stat-item {
  font-size: 28rpx;
}

.stat-strong {
  font-weight: 600;
}

.author-meta {
  display: flex;
  gap: 32rpx;
  font-size: 24rpx;
  opacity: 0.8;
}

.ip-location {
  color: #FFE6CC;
}

/* 统计网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rpx;
  background: white;
  border-radius: 16rpx 16rpx 0rpx 0rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  /* border-radius: 8px; */
  border-radius: 16rpx 16rpx 0 0;
  margin-left: 32rpx;
  margin-right: 32rpx;
}

.stats-grid .stat-item {
  background: white;
  padding: 32rpx;
  text-align: center;
}

.stats-grid .stat-number {
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;

  font-size: 32rpx;
  color: #062759;
  letter-spacing: 0;
  text-align: center;
  line-height: 72rpx;
  font-weight: 400;
}

.stats-grid .stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 视频详细信息 */
.video-details {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rpx;
  background: white;
  border-radius: 0 0 16rpx 16rpx;
  margin-bottom: 32rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  /* border-radius: 8px; */
  margin-left: 32rpx;
  margin-right: 32rpx;
}

.detail-item {
  background: white;
  padding: 32rpx;
  text-align: center;
}

.detail-number {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;

  color: #062759;
  letter-spacing: 0;
  text-align: center;
  line-height: 72rpx;
  font-weight: 400;

}

.detail-number1 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;

  color: #062759;
  letter-spacing: 0;
  text-align: center;
  line-height: 72rpx;
  font-weight: 400;

  width:240rpx;
  overflow: hidden;
  text-overflow: ellipsis; 
  white-space: nowrap;

}

.detail-number1.expanded {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;

  color: #062759;
  letter-spacing: 0;
  text-align: center;
  line-height: 72rpx;
  font-weight: 400;

  width:240rpx;
  overflow: visible;
  text-overflow: clip; 
  white-space: normal;

}

.detail-number2 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;

  color: #062759;
  letter-spacing: 0;
  text-align: center;
  line-height: 72rpx;
  font-weight: 400;

  width:200rpx;

}

.detail-label {
  font-size: 24rpx;
  color: #949494;
  letter-spacing: 0;
  text-align: center;
  line-height: 72rpx;
  font-weight: 400;
}


/* 分析标签 */
.analysis-tabs {
  display: flex;
  gap: 4rpx;
  margin-bottom: 32rpx;
  margin-left: 32rpx;
}

.tab-item {
  padding: 16rpx 32rpx;
  background: white;
  color: #666;
  border-radius: 16rpx;
  font-size: 28rpx;
  cursor: pointer;
  position: relative;
}

.tab-item.active {
  color: #4A90E2;
  font-weight: 600;
  background-color: #1F84FF;
  color: #ffffff;
}

/* 事件严重程度 */
.severity-indicator {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
  gap: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  border-radius: 8px;
  margin-left: 32rpx;
  margin-right: 32rpx;
}

.severity-icon {
  width: 80rpx;
  height: 80rpx;
  background: #FFE6E6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.severity-icon image {
  width: 84rpx;
  height: 84rpx;
}

.severity-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.severity-title {
  font-size: 28rpx;
  color: #666;
}

.severity-level {
  font-size: 32rpx;
  font-weight: 600;
  color: #f6750c;
}

/* 事件信息表格 */
.event-table {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 32rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  margin-left: 32rpx;
  margin-right: 32rpx;
}

.table-row {
  display: flex;
  border-bottom: 2rpx solid #f0f0f0;
}

.table-row:last-child {
  border-bottom: none;
}

.table-label {
  width: 240rpx;
  font-size: 28rpx;
  color: #666;
  background: #EDF6FF;
  padding: 16rpx 24rpx;
  margin-right: 32rpx;
  flex-shrink: 0;

  color: #46729E;
  letter-spacing: 0;
  line-height: 40rpx;
  font-weight: 400;
  padding-left: 40rpx;
}

.table-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  padding: 16rpx 0;
}

/* 联系方式 */
.contact-section {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  margin-left: 32rpx;
  margin-right: 32rpx;
}

.contact-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 32rpx;
  position: relative;
  padding-left: 24rpx;
}

.contact-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 32rpx;
  background: #FF6B6B;
  border-radius: 4rpx;
}

.contact-item {
  padding: 24rpx 0;
  border-bottom: 2rpx solid #f0f0f0;
}

.contact-item:last-child {
  border-bottom: none;
}

.contact-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.contact-phone {
  font-size: 32rpx;
  color: #4A90E2;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.contact-dept {
  font-size: 24rpx;
  color: #666;
}

.nav-left {
  position: relative;
  width: 88rpx;      /* 足够大的点击区域 */
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 48rpx;
  height: 48rpx;
  background: url('./img/left.png') no-repeat center/contain;
}

/* 7.25更新 */
/* .location-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.popup-content {
  background: white;
  padding: 32rpx;
  border-radius: 16rpx;
  width: 80%;
  text-align: center;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 24rpx;
}

.popup-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 32rpx;
  word-wrap: break-word;
}

.popup-close {
  background: #4A90E2;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 0rpx 0rpx;
  font-size: 28rpx;
  cursor: pointer;
} */

/* 7.28更新 */
.location-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
}

.popup-content {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  width: 80%;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  animation: fadeIn 0.3s ease;
}

.popup-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 32rpx;
  word-wrap: break-word;
}

.popup-close {
  background: #4A90E2;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  cursor: pointer;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}




	/* .popup-content {
		@include flex;
		align-items: center;
		justify-content: center;
		padding: 7px;
		height: 50px;
		background-color: #fff;
	}

	.text {
		font-size: 12px;
		color: #333;
	}

	.dialog,
	.share {
		display: flex;
		flex-direction: column;
	}

	.dialog-box {
		padding: 10px;
	}

	.dialog .button,
	.share .button {
		width: 100%;
		margin: 0;
		margin-top: 10px;
		padding: 3px 0;
		flex: 1;
	}

	.dialog-text {
		font-size: 14px;
		color: #333;
	} */
</style>
