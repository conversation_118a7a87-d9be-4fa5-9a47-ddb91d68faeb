{"name": "舆情系统", "appid": "__UNI__5CD4F90", "description": "", "versionName": "1.0.0", "versionCode": 1, "transformPx": false, "app-plus": {"usingComponents": true, "nvueCompiler": "uni-app", "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "nvueLaunchMode": "fast", "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "modules": {"OAuth": {}, "Speech": {}, "Geolocation": {}, "Maps": {}}, "distribute": {"android": {"permissions": ["<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.INTERNET\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.POST_NOTIFICATIONS\"/>", "<uses-permission android:name=\"android.permission.RECEIVE_BOOT_COMPLETED\"/>", "<uses-permission android:name=\"android.permission.SYSTEM_ALERT_WINDOW\"/>"], "abiFilters": ["arm64-v8a", "x86"], "networkSecurityConfig": "static/network_security_config.xml", "usesCleartextTraffic": true, "sdkConfigs": {"maps": {"amap": {"appkey_android": "2c8a4ecb1b19c0dcbe6bc794cabb37c1", "name": "amap_18845646734CQwJmg0T0"}}}}, "ios": {"UIBackgroundModes": ["audio"], "urlschemewhitelist": ["b<PERSON><PERSON><PERSON>", "iosamap"], "dSYMs": false}, "sdkConfigs": {"speech": {"ifly": {}}, "geolocation": {"amap": {"name": "amap_18845646734CQwJmg0T0", "__platform__": ["ios", "android"], "appkey_ios": "2c8a4ecb1b19c0dcbe6bc794cabb37c1", "appkey_android": "2c8a4ecb1b19c0dcbe6bc794cabb37c1"}}, "maps": {"amap": {"name": "amap_18845646734CQwJmg0T0", "appkey_ios": "2c8a4ecb1b19c0dcbe6bc794cabb37c1", "appkey_android": "2c8a4ecb1b19c0dcbe6bc794cabb37c1"}}}, "orientation": ["portrait-primary"], "icons": {"android": {"hdpi": "unpackage/res/icons/72x72.png", "xhdpi": "unpackage/res/icons/96x96.png", "xxhdpi": "unpackage/res/icons/144x144.png", "xxxhdpi": "unpackage/res/icons/192x192.png"}, "ios": {"appstore": "unpackage/res/icons/1024x1024.png", "ipad": {"app": "unpackage/res/icons/76x76.png", "app@2x": "unpackage/res/icons/152x152.png", "notification": "unpackage/res/icons/20x20.png", "notification@2x": "unpackage/res/icons/40x40.png", "proapp@2x": "unpackage/res/icons/167x167.png", "settings": "unpackage/res/icons/29x29.png", "settings@2x": "unpackage/res/icons/58x58.png", "spotlight": "unpackage/res/icons/40x40.png", "spotlight@2x": "unpackage/res/icons/80x80.png"}, "iphone": {"app@2x": "unpackage/res/icons/120x120.png", "app@3x": "unpackage/res/icons/180x180.png", "notification@2x": "unpackage/res/icons/40x40.png", "notification@3x": "unpackage/res/icons/60x60.png", "settings@2x": "unpackage/res/icons/58x58.png", "settings@3x": "unpackage/res/icons/87x87.png", "spotlight@2x": "unpackage/res/icons/80x80.png", "spotlight@3x": "unpackage/res/icons/120x120.png"}}}}, "uniStatistics": {"enable": true}}, "quickapp": {}, "quickapp-native": {"icon": "/static/logo.png", "package": "com.example.demo", "features": [{"name": "system.clipboard"}]}, "quickapp-webview": {"icon": "/static/logo.png", "package": "com.example.demo", "minPlatformVersion": 1070, "versionName": "1.0.0", "versionCode": 100}, "mp-weixin": {"appid": "wx0b02d589f1d8e728", "setting": {"urlCheck": false, "es6": true, "postcss": true, "minified": true}, "usingComponents": true, "permission": {"scope.userLocation": {"desc": "您的位置信息将用于路线规划"}}, "uniStatistics": {"enable": true}}, "mp-alipay": {"usingComponents": true, "uniStatistics": {"enable": true}}, "mp-baidu": {"usingComponents": true, "uniStatistics": {"enable": true}, "dynamicLib": {"editorLib": {"provider": "swan-editor"}}}, "mp-toutiao": {"usingComponents": true, "uniStatistics": {"enable": true}}, "mp-jd": {"usingComponents": true, "uniStatistics": {"enable": true}}, "h5": {"template": "template.h5.html", "router": {"mode": "history", "base": "./"}, "sdkConfigs": {"maps": {"qqmap": {"key": "TKUBZ-D24AF-GJ4JY-JDVM2-IBYKK-KEBCU"}, "tencent": {"key": "KMHBZ-KGB67-ALZXH-HBH2Y-GGARV-CUBOQ"}}}, "async": {"timeout": 20000}, "uniStatistics": {"enable": true}}, "vueVersion": "2", "mp-kuaishou": {"uniStatistics": {"enable": true}}, "mp-lark": {"uniStatistics": {"enable": true}}, "mp-qq": {"uniStatistics": {"enable": true}}, "quickapp-webview-huawei": {"uniStatistics": {"enable": true}}, "quickapp-webview-union": {"uniStatistics": {"enable": true}}, "uniStatistics": {"version": "2", "enable": true}}