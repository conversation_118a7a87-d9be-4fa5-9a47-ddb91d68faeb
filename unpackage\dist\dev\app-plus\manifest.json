{"@platforms": ["android", "iPhone", "iPad"], "id": "__UNI__5CD4F90", "name": "舆情系统", "version": {"name": "1.0.0", "code": 1}, "description": "", "launch_path": "__uniappview.html", "developer": {"name": "", "email": "", "url": ""}, "permissions": {"OAuth": {}, "Speech": {}, "Geolocation": {}, "Maps": {"coordType": "gcj02"}, "UniNView": {"description": "UniNView原生渲染"}}, "plus": {"useragent": {"value": "uni-app", "concatenate": true}, "splashscreen": {"target": "id:1", "autoclose": true, "waiting": true, "delay": 0}, "popGesture": "close", "launchwebview": {"render": "always", "id": "1", "kernel": "WKWebview"}, "statusbar": {"immersed": "supportedDevice", "style": "light", "background": "#007AFF"}, "usingComponents": true, "nvueCompiler": "uni-app", "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "distribute": {"orientation": ["portrait-primary"], "icons": {"android": {"hdpi": "unpackage/res/icons/72x72.png", "xhdpi": "unpackage/res/icons/96x96.png", "xxhdpi": "unpackage/res/icons/144x144.png", "xxxhdpi": "unpackage/res/icons/192x192.png"}, "ios": {"appstore": "unpackage/res/icons/1024x1024.png", "ipad": {"app": "unpackage/res/icons/76x76.png", "app@2x": "unpackage/res/icons/152x152.png", "notification": "unpackage/res/icons/20x20.png", "notification@2x": "unpackage/res/icons/40x40.png", "proapp@2x": "unpackage/res/icons/167x167.png", "settings": "unpackage/res/icons/29x29.png", "settings@2x": "unpackage/res/icons/58x58.png", "spotlight": "unpackage/res/icons/40x40.png", "spotlight@2x": "unpackage/res/icons/80x80.png"}, "iphone": {"app@2x": "unpackage/res/icons/120x120.png", "app@3x": "unpackage/res/icons/180x180.png", "notification@2x": "unpackage/res/icons/40x40.png", "notification@3x": "unpackage/res/icons/60x60.png", "settings@2x": "unpackage/res/icons/58x58.png", "settings@3x": "unpackage/res/icons/87x87.png", "spotlight@2x": "unpackage/res/icons/80x80.png", "spotlight@3x": "unpackage/res/icons/120x120.png"}}}, "google": {"permissions": ["<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.INTERNET\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.POST_NOTIFICATIONS\"/>", "<uses-permission android:name=\"android.permission.RECEIVE_BOOT_COMPLETED\"/>", "<uses-permission android:name=\"android.permission.SYSTEM_ALERT_WINDOW\"/>"], "abiFilters": ["arm64-v8a", "x86"], "networkSecurityConfig": "static/network_security_config.xml", "usesCleartextTraffic": true, "sdkConfigs": {"maps": {"amap": {"appkey_android": "2c8a4ecb1b19c0dcbe6bc794cabb37c1", "name": "amap_18845646734CQwJmg0T0"}}}}, "apple": {"UIBackgroundModes": ["audio"], "urlschemewhitelist": ["b<PERSON><PERSON><PERSON>", "iosamap"], "dSYMs": false}, "plugins": {"speech": {"ifly": {}}, "geolocation": {"amap": {"name": "amap_18845646734CQwJmg0T0", "__platform__": ["ios", "android"], "appkey_ios": "2c8a4ecb1b19c0dcbe6bc794cabb37c1", "appkey_android": "2c8a4ecb1b19c0dcbe6bc794cabb37c1"}}, "maps": {"amap": {"name": "amap_18845646734CQwJmg0T0", "appkey_ios": "2c8a4ecb1b19c0dcbe6bc794cabb37c1", "appkey_android": "2c8a4ecb1b19c0dcbe6bc794cabb37c1"}}, "audio": {"mp3": {"description": "Android平台录音支持MP3格式文件"}}}}, "uniStatistics": {"version": "2", "enable": true}, "allowsInlineMediaPlayback": true, "uni-app": {"compilerVersion": "4.56", "control": "uni-v3", "nvueCompiler": "uni-app", "renderer": "auto", "nvue": {"flex-direction": "column"}, "nvueLaunchMode": "fast"}, "launch_path": "__uniappview.html"}, "screenOrientation": ["portrait-primary", "portrait-secondary"]}