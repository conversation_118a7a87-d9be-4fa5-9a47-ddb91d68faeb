{"easycom": {"^u-(.*)": "@/uview-ui/components/u-$1/u-$1.vue"}, "pages": [{"path": "pages/views/index/VideoIndex", "style": {"navigationBarTitleText": "视频列表", "navigationStyle": "custom", "app-plus": {"bounce": "vertical", "titleNView": false}}}, {"path": "pages/views/index/VideoDetail", "style": {"navigationBarTitleText": "视频详情", "navigationStyle": "custom", "app-plus": {"bounce": "vertical", "titleNView": false}}}, {"path": "pages/views/login/index", "style": {"navigationBarTitleText": "登录管理", "navigationStyle": "custom"}}, {"path": "pages/views/index/index", "style": {"navigationBarTitleText": "首页", "navigationStyle": "custom", "app-plus": {"bounce": "vertical", "titleNView": false}}}], "globalStyle": {"pageOrientation": "portrait", "navigationBarTitleText": "Hello uniapp", "navigationBarTextStyle": "white", "navigationBarBackgroundColor": "#007AFF", "backgroundColor": "#F8F8F8", "backgroundColorTop": "#F4F5F6", "backgroundColorBottom": "#F4F5F6", "h5": {"navigationBarTextStyle": "black", "navigationBarBackgroundColor": "#F1F1F1"}}, "app-plus": {"modules": {"Maps": {}}, "distribute": {"android": {"permissions": ["<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.INTERNET\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>"]}}}}