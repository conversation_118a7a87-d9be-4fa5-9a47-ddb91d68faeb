let config = {
  baseApi: 'http://192.168.29.70:20006/api',
  baseApi1: 'http://192.168.29.70:20006',
  zhBaseApi: 'http://192.168.29.70:20003',
  YQSJBaseApi: 'http://192.168.1.124:5000',
  // YQSJBaseApi: 'http://101.43.246.156:5000',
  baseToken: '',
  mhBaseToken: ''
}

export function updateConfig(newConfig) {
  if (newConfig.token) {
    config.baseToken = newConfig.token
  }
  if (newConfig.mhToken) {
    config.mhBaseToken = newConfig.mhToken
  }
}

export default config